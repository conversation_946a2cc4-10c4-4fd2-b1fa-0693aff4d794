## SDC UI 迁移 SDC WebUI 指南
> 贡献者：cxyxhhuang(黄鑫杰)；最近更新时间：2021-01-26

注意要迁移前要确保SDC WebUI版本号要大于1.0.4-beta.3。

### 引入组件变化

SDC WebUI优化的一些组件名称，不再区分OA和OC，主要改动如下：

1. NavHeaderOa、NavHeaderOc两个组件合并为Header
2. MainOa、MainOc两个组件合并为Content
3. RouteView组件重命名为RouterView

具体变动参考下面组件变动

### 组件变动

#### Header 组件变动

SDC WebUI版本已经将 `NavHeaderOa` 和 `NavHeaderOc` 两个组件合并成 `Header`，通过新增属性 `scope` 去控制是oc组件还是oa组件，比如：

```html
<sdc-header :menus="menus" scope="oc"></sdc-header>
```

原先的menus属性由config.apps替代，头像属性可以通过avatar设置，和原来头像组件menus属性相同
```html
<script>
  export default {
    data() {
      return {
        config: {
          apps: {
            active: "", //当前选中的导航项的唯一标识值
            map: { 
              key: "id",  //唯一标识字段名
              text: "name",//显示文本字段名
              url: "link",//链接地址字段名
            },
            //数据集合
            data: [
              { 
                id: "tn-01", 
                name: "应用名称1", 
                link: "http://www.qq.com/", 
                target: "_self" //链接打开的位置
              }
            ]
          }
        },
        avatar: {
          url: 'http://km.oa.com/user/miraclehe', // 点击头像跳转链接
          avatarUrl: 'examples/assets/img/avatar1.png', // 显示的头像URL
          map: {
            url: 'link',
            text: 'name'
          },
          data: [
            { name: '个人信息', link: 'http://test.app.oa.com/info', type: 'info' },
            { name: '个人空间', link: 'http://test.app.oa.com/info', disabled: true },
            { name: '退出', divided: true, type: 'exit' } // 需要分隔一下
          ]
        }
      }
    }
  }
</script>

...
<sdc-header :menus="config.apps" scope="oc"></sdc-header>
```

类名也由 `sdc-nav-header-oa` 和 `sdc-nav-header-oc` 修改为 `sdc-header`，如果使用类名修改样式的，记得做相应的替换。

组件里对应的slot名字不变，新的组件包含oa和oc组件里的所有slot：logo/search/feedback/links/icons/avatar，Header组件具体使用请参考：[Header组件](/#/zh-CN/component/header)

#### Content 组件变动

SDC WebUI版本已经将 `MainOa` 和 `MainOc` 两个组件合并成 `Content`，通过新增属性 `scope` 去控制是oc组件还是oa组件，原先的config属性由menus替代，menus为原来config.menus属性。

```html
<script>
  export default {
    data() {
      return {
        config: {
          menus: {
            "active":"tn-01",
            "defaultOpeneds":"tn-01",
            "map":{ 
              "key": "id",
              "text": "name",
              "url": "link",
              "children" : "children"
            },
            "data":[
              { 
                "id": "tn-01", 
                "name": "返回门户", 
                "link": "http://hr.oa.com/", 
                "icon": "s-home",
                "click": (key) => {},
                "badge": 2,
                "badgeMax": 99
              }
            ]
          }
        }
      }
    }
  }
</script>

...
<sdc-content :menus="config.menus" :layout="['sidebar']"/>
```

若要显示侧边栏需要加上属性 `:layout="['sidebar']"`，Content组件具体使用请参考：[Content组件](/#/zh-CN/component/content)

#### Avatar 组件变动

1.0的代码
```html
<sdc-avatar
  :url="avatarUrl"
  :defaultUrl="defaultAvatarUrl"
  :menus="menus"
  menu-class="oc-drop-menu"
  @click="handleClick"
  name="cxyxhhuang">
</sdc-avatar>

<script>
  export default {
    data() {
      return {
        menus: [
          { text: '退出', type: 'logout' }
        ]
      }
    }
  }
</script>
```

2.0同样功能的代码，menus属性改为avatar，并且原有的menus值为现在avatar.data的值，url、avatarUrl属性放到menus里

```html
<sdc-avatar :avatar="avatar" @click="handleClick" name="cxyxhhuang"></sdc-avatar>

<script>
  export default {
    data() {
      return {
        avatar: {
          url: 'http://km.oa.com/user/miraclehe', // 点击头像跳转链接
          avatarUrl: 'examples/assets/img/avatar1.png', // 显示的头像URL
          map: {
            url: 'link',
            text: 'name'
          },
          data: [
            { name: '个人信息', link: 'http://test.app.oa.com/info', type: 'info' },
            { name: '个人空间', link: 'http://test.app.oa.com/info', disabled: true },
            { name: '退出', divided: true, type: 'exit' } // 需要分隔一下
          ]
        }
      }
    }
  }
</script>
```

Avatar组件具体使用请参考：[Avatar组件](/#/zh-CN/component/avatar)

### 其它改动

babel.config.js 需要修改插件代码

1.0版本代码
```js
module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset'
  ],
  plugins: [
    'component',
    {
      'libraryName': 'sdc-ui',
      'styleLibraryName': 'theme-grace'
    }, 'sdc-ui'
  ]
}
```

2.0版本代码
```js
module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset'
  ],
  plugins: [
    'component',
    {
      'libraryName': 'sdc-webui',
      'styleLibraryName': 'theme-grace'
    }, 'sdc-webui'
  ]
}
```

迁移如果遇到什么问题，请联系：[cxyxhhuang](wxwork://message?username=cxyxhhuang)
