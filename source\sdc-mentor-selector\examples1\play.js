import Vue from 'vue'
import VueRouter from 'vue-router'
import { DataBus, APP_INSTANCE_READY } from 'sdc-core'
import ElementUI from 'element-ui'
import SDCWebUI from 'main/index.js'
import App from './play.vue'
import 'sdc-theme/lib/index.css'
import 'packages/theme-grace/src/index.less'
import icon from './icon.json'

Vue.use(ElementUI)
Vue.use(SDCWebUI)
Vue.use(VueRouter)

Vue.prototype.$icon = icon // Icon 列表页用
const router = new VueRouter({
  mode: 'hash'
})

window.__context__ = new Vue({ // eslint-disable-line
  router,
  render: h => h(App)
}).$mount('#app')

DataBus.emit(APP_INSTANCE_READY, window.__context__)
