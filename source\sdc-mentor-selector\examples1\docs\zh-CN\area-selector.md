## AreaSelector 工作地选择器
> 贡献者：v_whaigong；最近更新时间：2023-12-06；

工作地选择器，用于选择员工的工作地。

### 基础用法

提供2种方式选择工作地，用 Tag 展示已选工作地。工作地选择器目前默认使用单选模式。<br/>
<br/>1. 展开级联面板选择； 2. 输入关键字搜索，使用下拉菜单展示筛选后的工作地<br/>

:::demo 默认单选，`v-model` 的值为当前被选中的工作地选项的 **value** 属性值。可通过 `map.multiple` 属性设置多选，`v-model` 的值为当前被选中的工作地选项的 **value** 属性值集合
```html
  <div class="block">
    <span class="demonstration">基础单选</span>
    <sdc-area-selector v-model="value1" placeholder="基础单选"/>
  </div>
  <div class="block">
    <span class="demonstration">基础多选</span>
    <sdc-area-selector ref="selector" v-model="value2" @change="selectorChange" :map="{multiple:true}" placeholder="基础多选"/>
  </div>
<script>
  export default {
    data(){
      return{
        value1: 37,
        value2: [37, 87]
      }
    },
    methods: {
      selectorChange(val) {
        console.log(val, this.value2, this.$refs.selector.getCheckedNodes())
      }
    }
  }
</script>
```
:::

### Tag展示

可以仅在输入框中显示选中项最后一级的工作地，而不是选中工作地所在的完整路径。可以折叠展示Tag。

:::demo 属性 `show-all-levels` 定义了是否显示完整的路径，将其赋值为`false`则仅显示最后一级；你也可以设置`collapseTags`属性将它们合并为一段文字；还可以设置`tagsLength`属性限制展示的Tag文字数量。
```html
<template>
  <div class="block">
    <span class="demonstration">仅展示最后一级</span>
    <sdc-area-selector v-model="value" :show-all-levels="false"/>
  </div>
  <div class="block">
    <span class="demonstration">折叠展示Tag</span>
    <sdc-area-selector ref="selector" v-model="value2" collapseTags :tagsLength="5" :show-all-levels="false" :map="{multiple:true}"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value: 37,
        value2: [37, 87]
      }
    }
  }
</script>
:::

### 多语言

:::demo 属性 `lang` 定义了展示语言，默认中文，可选值`en`。 
```html
<template>
  <sdc-area-selector v-model="value" lang="en"/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
:::

### 限制展示的大区或国家

:::demo 属性 `includeRegionList` 定义了展示大区的集合，默认全展示。 100——中国大陆、200——亚太、300——美洲、400——欧洲、500——中东及非洲。属性 `includeCountryList` 定义了展示国家的集合，默认全展示。
```html
  <div class="block">
    <span class="demonstration">限制展示大区</span>
    <sdc-area-selector v-model="value1" :includeRegionList="includeRegionList"/>
  </div>
  <div class="block">
    <span class="demonstration">限制展示国家</span>
    <sdc-area-selector v-model="value2" :includeCountryList="includeCountryList"/>
  </div>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: [],
        includeRegionList: [100, 200],
        includeCountryList: [1]
      }
    }
  }
</script>
```
:::

### 完整value值

绑定完整的value值数组。

:::demo 属性 `map.emitPath` 定义了value是否是完整的路径，默认只返回最后一级，将其赋值为`true`则返回完整value,单选为`[地区id, 国家id, 城市id]`,多选为`[[地区id, 国家id, 城市id]]`。
```html
<template>
  <sdc-area-selector v-model="value" :map="map"/>
</template>
<script>
  export default {
    data(){
      return{
        value: [200, 22, 87],
        map:{
          emitPath:true
        }
      }
    }
  }
</script>
```
:::
<!--  1.0.14-beta.46版本移除
### 文本域

用于多行展示被选择的工作地，通过设置 `textarea` 属性启用

:::demo 可通过 `height` 属性设置高度
```html
<template>
  <sdc-area-selector textarea v-model="value" :height="130" :map="{multiple:true}"/>
</template>
<script>
  export default {
    data(){
      return{
        value: [37, 87]
      }
    }
  }
</script>
```
::: -->

### 展示层级

设置显示的级联层级数

:::demo 可通过 `level` 来设置显示层级数，默认三级。
```html
<template>
  <div class="block">
    <span class="demonstration">仅展示一级</span>
    <sdc-area-selector v-model="value1" :level="1"/>
  </div>
  <div class="block">
    <span class="demonstration">仅展示二级</span>
    <sdc-area-selector v-model="value2" :map="{multiple: true }" :level="2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: [11]
      }
    }
  }
</script>
```
:::

### 尺寸

:::demo 可通过 `size` 属性指定输入框的尺寸，除了默认的大小外，提供了**small**尺寸
```html
<template>
  <div class="block">
    <span class="demonstration">默认尺寸</span>
    <sdc-area-selector v-model="value1" placeholder="默认尺寸"/>
  </div>
  <div class="block">
    <span class="demonstration">较小尺寸</span>
    <sdc-area-selector v-model="value2" size="small"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: ''
      }
    }
  }
</script>
```
:::

### 数据源

选择器内置的工作地数据是在初始时从远程服务器拉取的，用户可替换自己的数据源

:::demo `getLocationList` 属性接收一个 `Promise`，完成状态时的返回值为有清晰层级结构的工作地集合，如果数据源字段与选择器默认要求的 **value，label，children** 不一致，可通过 `map` 属性进行映射，
```html
<template>
  <sdc-area-selector v-model="value" :map="map" :getLocationList="customGetRegion" showTotal></sdc-area-selector>
</template>
<script>
  export default {
    created() {
      this.customGetRegion = new Promise((resolve, reject) => {
        resolve(this.remoteData)
      })
    },
    data(){
      return{
        value: 3,
        customGetRegion: '',
        map: {
          value: 'value',
          label: 'label'
          // children: 'children'
        },
        remoteData: [{
          value: 1,
          label: '东南',
          children: [{
            value: 2,
            label: '上海',
            children: [
              { value: 3, label: '普陀' },
              { value: 4, label: '黄埔' },
              { value: 5, label: '徐汇' }
            ]
          }, {
            value: 6,
            label: '江苏',
            children: [
              { value: 7, label: '南京' },
              { value: 8, label: '苏州' },
              { value: 9, label: '无锡' }
            ]
          }, {
            value: 10,
            label: '浙江',
            children: [
              { value: 11, label: '杭州' },
              { value: 12, label: '宁波' },
              { value: 13, label: '嘉兴' }
            ]
          }]
        }, {
          value: 17,
          label: '西北',
          children: [{
            value: 18,
            label: '陕西',
            children: [
              { value: 19, label: '西安' },
              { value: 20, label: '延安' }
            ]
          }, {
            value: 21,
            label: '新疆维吾尔族自治区',
            children: [
              { value: 22, label: '乌鲁木齐' },
              { value: 23, label: '克拉玛依' }
            ]
          }]
        }]
      }
    }
  }
</script>
```
:::

### AreaSelector Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | Array | — | — |
| size | 输入框尺寸 | string | small | — |
| lang | 语言 | string | en | — |
| level | 层级数 | Number | 1、2、3 | 3 |
| disabled | 是否禁用 | boolean | — | false |
| textarea | 文本框（1.0.14-beta.46版本移除） | boolean | — | false |
| height | 文本框高度（1.0.14-beta.46版本移除） | Number | — | 130 |
| width | 输入框宽度（1.0.14-beta.46版本移除） | Number | — | — |
| showTotal | 是否显示后置的已选数量 | boolean | — | false |
| placeholder | 占位符 | string | — | — |
| separator | 选项分隔符 | string | — | 斜杠'/' |
| collapseTags | 多选模式下是否折叠Tag | boolean | — | false |
| tagsLength | Tag最大展示文字数, 最小1 | number | — | 13 |
| showAllLevels | 输入框中是否显示选中值的完整路径 | boolean | — | true |
| includeRegionList | 可展示的大区集合 | Array | — | [100, 200, 300, 400, 500] |
| includeCountryList | 可展示的国家集合（默认全展示） | Array | — | [] |
| map | 映射配置，具体见下表 | object | — | — |
| getLocationList | 获取层级工作地数据的方法 | Promise | — | — |

### AreaSelector Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项, 包含label、value、path数组、fullName |

### AreaSelector Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| clearSelected | 用于清空选中项 | — |
| getCheckedNodes | 获取选中的节点 | (leafOnly) 是否只是叶子节点，默认值为 false |


### map
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value | 指定选项的值为选项对象的某个属性值 | string | — | 'item_id' |
| label | 指定选项标签为选项对象的某个属性值 | string | — | 'item_name_cn' |
| children | 指定选项的子选项为选项对象的某个属性值 | string | — | 'children' |
| emitPath | 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 | boolean | — | false |
| multiple | 是否多选 | boolean | — | false |
