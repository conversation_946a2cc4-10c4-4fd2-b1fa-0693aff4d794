## PositionCascader 职位级联选择器
> 贡献者：kenoxia(夏超男)；ivanxu(徐凯)；最近更新时间：2021-12-27；

当能一次性获取有清晰层级结构的职位集合时，可通过级联选择器逐级查看并选择。

### 基础用法

适用广泛的基础选择，提供2种方式选择职位，用 Tag 展示已选职位。职位级联选择器目前默认使用多选模式<br/>
<br/>1. 展开级联面板选择； 2. 输入关键字搜索，使用下拉菜单展示筛选后的职位<br/>
<br/>提醒: 请避免在选择后动态改变选择器的宽度，这会造成 Tag 区域样式显示问题

:::demo `v-model` 的值为当前被选中的职位选项的 **value** 属性值
```html
<template>
  <div class="block">
    <span class="demonstration">基础单选</span>
    <sdc-position-cascader v-model="value1" :map="map" placeholder="基础单选" />
  </div>
  <div class="block">
    <span class="demonstration">基础多选</span>
    <sdc-position-cascader v-model="value2" placeholder="基础多选" @change="change"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: [],
        map: {
          multiple: false
        }
      }
    },
    methods: {
      change(e) {
        console.log(e, this.value2)
      }
    }
  }
</script>
```
:::

<!-- ### 设置初始选中项

目前不建议和不支持通过修改 `v-model` 绑定的数据值设置初始选中项

:::demo 可通过 `setSelected` 方法设置初始选中项，它的输入参数是一个 **value** 值，或多个 **value** 值组成的数组
```html
<template>
  <sdc-position-cascader ref="selector" v-model="value" multiple/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    },
    mounted() {
      const initial = [101]
      this.$refs.selector.setSelected(initial)
    }
  }
</script>
```
::: -->

### 定制需要显示的职位簇

:::demo 可通过 `includeClans` 方法设置需要显示的职位簇，它是由对应 **value** 值组成的数组
```html
<template>
  <sdc-position-cascader ref="selector" v-model="value" :includeClans="includeClans"/>
</template>
<script>
  export default {
    data(){
      return{
        value: [],
        includeClans: [1, 6, 14]
      }
    }
  }
</script>
```
:::

### 级联面板展示层级

设置显示的级联层级数

:::demo 可通过 `level` 来设置显示层级数，默认三级。
```html
<template>
  <div class="block">
    <span class="demonstration">仅展示一级</span>
    <sdc-position-cascader v-model="value1" :level="1"/>
  </div>
  <div class="block">
    <span class="demonstration">仅展示二级</span>
    <sdc-position-cascader ref="selector" v-model="value2" multiple :level="2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [1],
        value2: [1]
      }
    }
  }
</script>
```
:::
### 多选Tag展示
:::demo 多选模式下，默认情况下会展示所有已选中的选项的Tag，你可以使用`collapseTags`来折叠Tag。 可以使用`tagsLength`来设置Tag最大展示文字数
```html
  <div class="block">
    <span class="demonstration">默认显示所有Tag</span>
    <sdc-position-cascader v-model="value1"/>
  </div>
  <div class="block">
    <span class="demonstration">折叠展示Tag</span>
    <sdc-position-cascader v-model="value2" collapseTags :tagsLength="7"/>
  </div>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 仅展示最后一级

可以仅在输入框中显示选中项最后一级的职级，而不是选中职级所在的完整路径。

:::demo 属性 `showAllLevels` 定义了是否显示完整的路径，将其赋值为`false`则仅显示最后一级。
```html
<template>
  <sdc-position-cascader v-model="value" :showAllLevels="false" :filterable="false"/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
```
:::
### 多语言

:::demo 属性 `lang` 定义了展示语言，默认中文，可选值`en`。 
```html
<template>
  <sdc-position-cascader v-model="value" lang="en" @change="change"/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    },
    methods: {
      change(e) {
        console.log(e)
      }
    }
  }
</script>
```
:::

### 可搜索

:::demo 可以使用`filterable`属性来开启搜索功能, 默认开启搜索功能。
```html
  <div class="block">
    <span class="demonstration">不可搜索</span>
    <sdc-position-cascader v-model="value1" :filterable="false"/>
  </div>
  <div class="block">
    <span class="demonstration">可搜索</span>
    <sdc-position-cascader v-model="value2" />
  </div>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 尺寸

:::demo 可通过 `size` 属性指定输入框的尺寸，除了默认的大小外，提供了**small**尺寸
```html
<template>
  <div class="block">
    <span class="demonstration">默认尺寸</span>
    <sdc-position-cascader v-model="value1"/>
  </div>
  <div class="block">
    <span class="demonstration">较小尺寸</span>
    <sdc-position-cascader size="small" v-model="value2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 数据源

选择器内置的职位数据是在初始时从远程服务器拉取的，用户可替换自己的数据源

:::demo `getPositionData` 属性接收一个 `Promise`，完成状态时的返回值为有清晰层级结构的职位集合，如果数据源字段与选择器默认要求的 **value，label，children** 不一致，可通过 `map` 属性进行映射，
```html
<template>
  <sdc-position-cascader v-model="value" :map="map" :getPositionData="customGetPositionData"></sdc-position-cascader>
</template>
<script>
  export default {
    created() {
      this.customGetPositionData = () => new Promise((resolve, reject) => {
        resolve(this.remoteData)
      })
    },
    data(){
      return{
        value: [],
        customGetPositionData: '',
        map: {
          value: 'id',
          label: 'name'
          // children: 'children'
        },
        remoteData: [
          {
            id: "1",
            name: "技术类",
            children: [{
              id: "1-1",
              name: "软件开发",
              children:
              [
                { id: "1-1-1", name: "后台开发" },
                { id: "1-1-2", name: "测试开发" }
              ]
            }, {
              id: "1-2",
              name: "技术运营"
            }, {
              id: "1-3",
              name: "技术研究",
              children: [
                { id: "1-3-1", name: "机器学习" },
                { id: "1-3-2", name: "计算机视觉" }
              ]
            }]
          },
          {
            id: "2",
            name: "产品类",
            children: [
              { id: "2-1", name: "产品经理" },
              { id: "2-2", name: "游戏策划" }
            ]
          }
        ]
      }
    }
  }
</script>
```
:::

### PositionCascader Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | Array | — | — |
| size | 输入框尺寸 | string | small | — |
| level | 层级数 | Number | 1、2、3 | 3 |
| lang | 语言 | string | 中文: zh，英文: en | zh |
| disabled | 是否禁用 | boolean | — | false |
| collapseTags | 多选模式下是否折叠Tag | boolean | — | false |
| tagsLength | Tag最大展示文字数, 最小1 | number | — | 13 |
| showTotal | 是否显示后置的已选数量 | boolean | — | true |
| placeholder | 占位符 | string | — | — |
| filterable | 是否可搜索选项 | boolean | — | true |
| separator | 选项分隔符 | string | — | 斜杠'/' |
| includeClans | 由需要包含的职位簇value组成的数组, 不设置时默认全部加载, 具体职位簇见下表 | Array | — | — |
| map | 映射配置，具体见下表 | object | — | — |
| getPositionData | 获取层级职位数据的方法 | Promise | — | — |

### PositionCascader Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项, 包含label、value、path数组、fullName |

### PositionCascader Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| clearSelected | 用于清空选中项 | — |
| getCheckedNodes | 获取选中的节点 | (leafOnly) 是否只是叶子节点，默认值为 false |


### map
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value | 指定选项的值为选项对象的某个属性值 | string | — | 'value' |
| label | 指定选项标签为选项对象的某个属性值 | string | — | 'label' |
| children | 指定选项的子选项为选项对象的某个属性值 | string | — | 'children' |
| emitPath | 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 | boolean | — | false |
| multiple | 是否多选 | boolean | — | true |

### PostionClan
| 职位簇值    | 名称          | 职位簇值   | 名称         | 职位簇值   | 名称         |
|---------- |-------------- |---------- |-------------- |---------- |-------------- |
| 1 | 管理族 | 6 | 操作族 | 14 | 产品/项目族（PD） |
| 15 | 管理族（LS）| 17 | 专业族（SC） | 18 | 技术族（TE）|
| 19 | 市场族（MA） | 20 | 客服族 | 22 | 设计族（DG） |
| 101 | Tencent Global |
