## DictSelector 基础字典选择器
> 贡献者：miraclehe(何名宇)；kenoxia(夏超男)；最近更新时间：2020-3-10；

根据不同的字典名称，使用下拉菜单展示对应的选项并选择内容。

### 基础用法

适用于广泛的基础单选

:::demo 使用 `type` 属性来设置字典名称， `v-model` 的值为当前被选中的选项的 `value` 属性值
```html
<template>
  <sdc-dict-selector v-model="value" type="1" placeholder="请选择"/>
</template>
<script>
  export default {
    data(){
      return{
        value: ''
      }
    }
  }
</script>
```
:::

### 基础多选

适用性较广的基础多选，用Tag展示已选内容

:::demo 设置`multiple`属性即可启用多选，此时`v-model`的值为当前选中值所组成的数组。默认情况下选中值会以 Tag 的形式展现，你也可以设置`collapse-tags`属性将它们合并为一段文字; 可以使用`tagsLength`来设置Tag最大展示文字数;  可以使用`filterable`来设置是否可筛选。
```html
<template>
  <sdc-dict-selector v-model="value1" multiple type="1"/>
  <sdc-dict-selector v-model="value2" multiple type="1" :tagsLength="3" :filterable="false" collapse-tags style="margin-left: 20px;"/>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 有排除选项

排除选项不会显示在下拉菜单中

:::demo 使用 `exclude` 属性来设置排除选项，它的值是一个由排除选项的 `label` 属性值所组成的数组
```html
<template>
  <sdc-dict-selector v-model="value" type="2" :exclude="exclude" placeholder="请选择"/>
</template>
<script>
  export default {
    data(){
      return{
        value: '',
        exclude: ['正式', '外聘', '实习']
      }
    }
  }
</script>
```
:::

### 自定义选项

可以自定义下拉菜单中的选项

:::demo 使用 `data` 属性来设置选项数据源，或者使用`promise`来设置访问选项数据源的方法，此时 `type` 属性失效。 如果你的数据源中不包含 `value` 和 `label` 默认字段，可以通过 `valueMap` 和`labelMap` 属性来指定
```html
<template>
  <sdc-dict-selector v-model="value1" :data="items1" labelMap="group" valueMap="mark"/>
  <sdc-dict-selector v-model="value2" :promise="promise" style="margin-left: 20px;"/>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: '',
        items1: [
          { group: 'T族', mark: '选项1' },
          { group: 'P族', mark: '选项2' }
        ],
        promise: new Promise((resolve, reject) => {
          // 获取远程数据源
          const remoteData = [
            { label: 'M族', value: '选项1' },
            { label: 'S族', value: '选项2' }
          ]
          resolve(remoteData)
        }),
      }
    }
  }
</script>
```
:::

### DictSelector Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | boolean/string/number | — | — |
| type | 字典名称, 目前支持的值见下表 | string/number | — | — |
| multiple | 是否多选 | boolean | — | false |
| size | 输入框尺寸 | string | medium/small/mini | — |
| disabled | 是否禁用 | boolean | — | false |
| collapse-tags | 多选时是否将选中值按文字的形式展示 | boolean | — | false |
| tagsLength | 多选时Tag最大展示文字数, 最小1 | number | — | 13 |
| placeholder | 占位符 | string | — | 请选择 |
| filterable | 是否可搜索选项 | boolean | — | true |
| no-match-text | 搜索条件无匹配时显示的文字 | string | — | 无匹配数据 |
| exclude | 排除选项 | array | — | [ ] |
| data | 自定义选项 | array | — | [ ] |
| promise | 覆盖组件内部获取选项数据源的默认方法，`resolve` 函数的参数需要是一个由选项组成的数组 | Promise | — | — |
| labelMap | 指定选项标签为选项对象的某个属性值 | string | — | 'label' |
| valueMap | 选项的值为选项对象的某个属性值 | string | — | 'value' |
| customClass | 自定义类名 | string | — | — |

### DictSelector Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项 |
| visible-change | 下拉框出现/隐藏时触发 | 出现则为 true，隐藏则为 false |
| remove-tag | 多选模式下移除tag时触发 | 移除的tag值 |
| clear | 单选模式下用户点击清空按钮时触发 | — |
| blur | 当 input 失去焦点时触发 | (event: Event) |
| focus | 当 input 获得焦点时触发 | (event: Event) |

### Option Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value | 选项的值 | string/number/object | — | — |
| label | 选项的标签，若不设置则默认与 `value` 相同 | string/number | — | — |

### Type

字典项参考核心人事业务字段管理：<a href="https://hr-core.woa.com/web/dictionaryView/dictionary?key=30" target="_blank">https://hr-core.woa.com/web/dictionaryView/dictionary?key=30</a>

<img src="../../assets/img/dict-selector/img1.png" width="100%">
