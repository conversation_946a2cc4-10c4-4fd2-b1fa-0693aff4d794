.demo-position-cascader {
  .block {
    padding: 30px 0;
    border-right: solid 1px #eff2f6;
    width: 50%;
    box-sizing: border-box;
    &:first-child {
      padding-right: 20px;
    }
    &:last-child {
      border-right: none;
      padding-left: 20px;
    }
  }
  .demonstration {
    text-align: center;
    display: block;
    color: #8492a6;
    font-size: 14px;
    margin-bottom: 20px;
  }
  .sdc-position-cascader {
    width: 390px;
  }
}

.demo-position-cascader .source > div {
  display: flex;
}