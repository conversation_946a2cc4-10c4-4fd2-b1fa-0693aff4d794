## Content 内容区域
> 贡献者：cxyxhhuang(黄鑫杰)；最近更新时间：2021-01-26

内容区域，包含siderbar组件

### 基础用法

空的content，content组件内部包含router-view，可以结合vue-router使用

:::demo
```html
<template>
  <sdc-content />
</template>
```
:::

包含侧边栏组件

:::demo 要显示侧边栏，要添加`:layout="['sidebar']"`，然后通过传入`menus`属性来设置指定的侧边栏菜单，若要设置某个菜单禁止点击，请在data的属性添加`disabled：true`，参考下面代码。在menus属性里添加defaultOpeneds属性可以设置默认展开的子菜单，defaultOpeneds为包含菜单key的数组。支持通过添加badge属性在sidebar子项右上角添加角标，通过设置badgeMax属性设置角标最大值，超过显示+号
```html
<template>
  <sdc-content :menus="menus" :layout="['sidebar']" :showMenu="true" @menuContextmenu="handleMenuContextmenu"/>
</template>
<script>
  export default {
  data() {
    return {
      menus: {
        active: '',
        uniqueOpened: true, // 是否只保持一个子菜单的展开，默认false
        defaultOpeneds: ['tn-03'], // 默认打开的子菜单，data中key的数组
        map: {
          key: 'id',
          text: 'name',
          url: 'link'
        },
        data: [
          { id: 'tn-01',
            name: '办事大厅',
            link: 'http://hr.oa.com/',
            icon: 'el-icon-place',
            parentId: '0',
            level: 1,
            click: (key) => {}, // 点击事件，但存在link属性的情况下无效，参数为对应项的key
            badge: 100, // badge角标值
            badgeMax: 99 // badgeMax角标最大值，超过显示+号
          }, 
          { id: 'tn-02', name: '个人信息', link: 'http://hrstaff.oa.com/hr/HRStaff', icon: 'el-icon-user', parentId: '0', level: 1 },
          {
            id: 'tn-03',
            name: '组织发展',
            icon: 'el-icon-coordinate',
            parentId: '0',
            level: 1,
            offset: 0, // 折叠时，浮窗位置偏移量，默认0，Number类型
            children: [
              { id: 'tn-03-01', name: '首页', link: 'http://od.oa.com/organization/default.aspx', parentId: 'tn-03', level: 2 },
              { id: 'tn-03-02', name: '360度评估', link: 'http://yunassess.oa.com/yunassess/taskList', parentId: 'tn-03', level: 2, disabled: true }, // 设置disabled为true，禁用子项
              { id: 'tn-03-03', name: '组织人才盘点', link: 'http://od.oa.com/organization/OrgPages/MyOrgTask.aspx', parentId: 'tn-03', level: 2 },
              { id: 'tn-03-04', name: '任职资格测算', link: 'http://od.oa.com/HRQualification/', parentId: 'tn-03', level: 2 }
            ]
          },
          {
            id: 'tn-04',
            name: '组织发展2',
            icon: 'el-icon-coordinate',
            parentId: '0',
            level: 1,
            children: [
              { id: 'tn-04-02', name: '360度评估', link: 'http://yunassess.oa.com/yunassess/taskList', parentId: 'tn-04', level: 2, disabled: true }
            ]
          }
        ]
      }
    }
  },
  methods: {
    handleMenuContextmenu(event, menu) {
      console.log('handleMenuContextmenu', event, menu)
    }
  }
}
</script>
```
:::

### Content Attributes
| 参数            | 说明                     | 类型    | 可选值                                             | 默认值                                                                                          |
| --------------- | ------------------------ | ------- | -------------------------------------------------- | ----------------------------------------------------------------------------------------------- |
| menus     | 头像菜单配置 | object  | —                                                  | —
| layout     | 内容区域包含组件 | object  | 要使用侧边栏，请设置为['sidebar']                   | —
| showMenu     | 是否显示sidebar | object  | 属性为boolean，这个值要生效要设置layout属性为['layout']，此属性主要是为了部分页面不需要侧边栏而添加                   | true

### Content Events
| 事件名称       | 说明                             | 回调参数          |
| -------------- | -------------------------------- | ----------------- |
| toggleCollapse | 左侧菜单切换展开折叠时回调       | 菜单当前折叠状态，true为折叠，false为展开      |
| menuContextmenu | 左侧菜单右键点击事件    | event 鼠标右击事件对象, menu 右击点的菜单项      |


### Layout Methods
| 事件名称        | 说明                    | 参数          |
| -------------- | -------------------------------- | ----------------- |
| changeCollapse | 修改左侧菜单折叠状态     | 折叠状态      |