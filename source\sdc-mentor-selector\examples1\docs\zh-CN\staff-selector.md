## StaffSelector 员工选择器
> 贡献者：miraclehe(何名宇)；kenoxia(夏超男)；最近更新时间：2024-05-15；

用于选择员工，涉及的数据源均为远程数据

### 基础用法

适用广泛的基础选择，提供3种方式选择员工，用 Tag 展示已选员工<br/>
<br/>1. 输入关键字搜索，使用下拉菜单展示筛选后的员工； 2. 点击右侧按钮打开弹窗，懒加载员工树选择；
<br/>3. 将 `StaffName;StaffName;` 或者 `StaffName\nStaffName (\n代表回车)` 形式的字符串 **粘贴** 进输入框，从服务器获取对应员工项，例子：kenoxia(夏超男);miraclehe(何名宇); 或者：kenoxia(夏超男)\nmiraclehe(何名宇)<br/>
<br/>提醒: 请避免在选择后动态改变选择器的宽度，这会造成 Tag 区域样式显示问题

:::demo `v-model` 的值为当前被选中的员工选项的 **StaffID** 属性值，单选模式下需删除原有已选项才能进行再次选择。设置 `multiple` 属性即可启用多选，此时`v-model`的值为当前选中值所组成的数组
```html
<template>
  <div class="block">
    <span class="demonstration">单选模式</span>
    <sdc-staff-selector v-model="value1" selectClass="selectClass" modalClass="modalClass" showFullTag></sdc-staff-selector>
  </div>
  <div class="block">
    <span class="demonstration">多选模式</span>
    <sdc-staff-selector multiple v-model="value2" :includeDimission="true" :includeOnBoarding="true" :includePartTimePost="true"></sdc-staff-selector>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: []
      }
    }
  }
</script>
```
:::

### 设置初始选中项

由于选择器所需的员工选项至少需要包含 `StaffID` 、`StaffName` 等属性，而本地没有完整的员工数据，因此不能简单通过修改 `v-model` 值来设置初始选择项

:::demo 可通过 `setSelected` 方法设置初始选中项，它的输入参数是一个对象或该类对象组成的数组，对象应包含 `{ StaffName, StaffID, Avatar(建议) }` 等属性，如果用户初始选中项字段与选择器默认的不一致，可通过 `props` 属性配置，同时，用户通过change事件得到的选中项里的字段名也会修改为 `props` 里定义的名称
```html
<template>
  <div class="block">
    <sdc-staff-selector ref="selector1" v-model="value1" multiple @change="change"/>
  </div>
  <div class="block">
    <sdc-staff-selector ref="selector2" :props="myProps" multiple v-model="value2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: [],
        myProps: {
          staffName: 'name',
          staffID: 'id',
          // avatar: 'Avatar'
        }
      }
    },
    methods: {
      change(val) {
        console.log(val, this.value1)
      }
    },
    mounted() {
      const initial1 = [ { StaffName: 'shanzhang(张三)', StaffID: 123456 }]
      this.$refs.selector1.setSelected(initial1)
      const initial2 = [{ name: 'lizhang(李四)', id: 654321 }]
      this.$refs.selector2.setSelected(initial2)
    }
  }
</script>
```
:::

### 限制选项范围

用于仅提供某组织下的员工选择<br/>
<br/>提醒: 动态切换组织时，已有选项需手动调用方法清空（考虑与组织选择器联动和数据回显等场景，选择器不会主动对已有选项做处理）

:::demo 可通过 `range.unitID` 属性设置仅选择某组织下的**子级**员工;可通过 `range.isContainSubStaff` 属性设置是否包含子级组织下的员工;可通过 `range.managerPositionLevelIdList` 属性设置仅选择对应职级的员工;可通过 `range.contractCompanyID` 属性设置哪个合同主体的公司;可通过 `range.staffTypeIdList` 属性设置仅选择对应员工类型的员工;可通过 `defaultExpandedKeys` 属性设置一级默认展开节点
```html
<template>
  <div class="block">
    <sdc-staff-selector v-model="value1" :range="range1" placeholder="请选择" :defaultExpandedKeys="[4791]"></sdc-staff-selector>
  </div>
  <div class="block">
    <sdc-staff-selector v-model="value2" :range="range2" placeholder="请选择" :defaultExpandedKeys="[4791]"></sdc-staff-selector>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        range1: {
          unitID: 4791, // 根组织ID
          contractCompanyID: 2, // 合同主体公司的ID
          isContainSubStaff: true, // 展示下级组织及员工
          managerPositionLevelIdList: [61] // 限制职级ID集合
        },
        value2: [],
        range2: {
          unitID: 4791, // 根组织ID
          isContainSubStaff: true, // 展示下级组织及员工
          staffTypeIdList: [9] // 员工类型
        }
      }
    }
  }
</script>
```
:::

### 曾用名查询

模糊查询包含曾用名<br/>
<br/>提醒: 模糊查询时，搜索曾用名只支持`英文名`搜索，搜索现用名支持`英文+(中文)`

:::demo 可通过 `useFormerNameSearch` 属性设置使用曾用名查询;
```html
<template>
    <sdc-staff-selector v-model="value" useFormerNameSearch placeholder="请选择"></sdc-staff-selector>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
```
:::

### 文本域

用于多行展示被选择的员工，通过设置 `textarea` 属性启用

:::demo 可通过 `height` 属性设置高度
```html
<template>
  <sdc-staff-selector v-model="value" :height="170" textarea multiple placeholder="请选择"></sdc-staff-selector>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
```
:::

### 尺寸

:::demo 可通过 `size` 属性指定输入框的尺寸，除了默认的大小外，提供了**medium**、**small**尺寸
```html
<template>
  <div class="block size-block">
    <span class="demonstration">默认尺寸</span>
      <sdc-staff-selector v-model="value1"></sdc-staff-selector>
  </div>
  <div class="block size-block">
    <span class="demonstration">中等尺寸</span>
    <sdc-staff-selector size="medium" v-model="value2"></sdc-staff-selector>
  </div>
  <div class="block size-block">
    <span class="demonstration">小尺寸</span>
    <sdc-staff-selector size="small" v-model="value3"></sdc-staff-selector>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: '',
        value3: ''
      }
    }
  }
</script>
```
:::

### 弹窗插入到body中

:::demo 可通过 `modalAppendToBody` 属性设置弹窗插入位置。默认false，设置为true时，弹窗会插入到body中。
```html
<template>
  <sdc-staff-selector v-model="value" modalAppendToBody/>
</template>
<script>
  export default {
    data(){
      return{
        value: ''
      }
    }
  }
</script>
```
:::

### 数据源

员工数据均从远程服务器获取，内置了4个默认数据源接口方法满足选择器的功能需求，用户如需替换自己的数据源，需按规范定义接口方法和数据结构，通过4个对应的属性传入`Function`<br/>每个`Function`接受一个输入参数用于指明需获取的员工数据，返回一个`Promise`对象，除 **`getTreeData`** 以外，完成状态的结果值为包含**员工对象**的**数组**，**员工对象**至少包含 **`{ StaffName, StaffID, Avatar(建议) }`** 三个属性<sup>**[1]**</sup>

:::demo **`getDataList`** 用于输入关键字筛选员工的场景。输入参数为一个`String`类型的关键字字符串, Promise的结果值如 **[1]** 所示<br/><br/> **`getPasteResult`** 用于粘贴姓名选择员工的场景。输入参数为一个`String`类型的，`StaffName;StaffName;` 或者 `StaffName\nStaffName (\n代表回车)` 形式的字符串，Promise的结果值如 **[1]** 所示<br/><br/>**`getTreeData`** 用于点击员工树中的组织节点，懒加载其子节点的场景。输入参数为一个能指明组织的`UnitID`，请注意，初始获取第一级节点时，输入的 **UnitID** 为 **0** ，Promise完成状态的结果值为包含`{ staff, unit }`属性的对象，`staff`属性值是一个包含**员工对象**的**数组**，要求同 **[1]** ， `unit`属性值是一个包含**组织对象**的**数组**，**组织对象**至少包含 `{ UnitName, UnitID }` 两个属性<br/><br/>**`getChildrenData`** 用于点击员工树中组织节点的多选框，批量选择员工的场景（考虑数据量，目前仅选择**子级**员工）。输入参数为一个能指明组织的`UnitID`，Promise的结果值如 **[1]** 所示

```html
<template>
  <sdc-staff-selector v-model="value" multiple modalWidth="1000px"
    :getDataList="customGetDataList"
    :getPasteResult="customGetPasteResult"
    :getTreeData="customGetTreeData"
    :getChildrenData="customGetChildrenData">
  </sdc-staff-selector>
</template>
<script>
  export default {
    data(){
      return{
        value: [],
        remoteData: [
          { StaffName: 'fourli(李四)', StaffID: 288888 },
          { StaffName: 'fivewang(王五)', StaffID: 123321 },
          { StaffName: 'sixzhao(赵六)', StaffID: 666666 },
          { StaffName: 'sevenchen(陈七)', StaffID: 233333 },
          { StaffName: 'ninelin(林九)', StaffID: 111111 }
        ]
      }
    },
    methods: {
      /**
       * @method 模糊搜索对应的员工
       * @param {String} name 关键字字符串
       * @returns 返回带有员工列表数据的promise
       */
      customGetDataList(name) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          let data = []
          data = this.remoteData.filter(item => item.StaffName.indexOf(name) !== -1)
          resolve(data)
        })
      },
      /**
       * @method 粘贴员工姓名字符串获取对应的员工
       * @param {String} nameString 一个以上员工姓名组成的字符串
       * @returns 返回带有员工列表数据的promise
       */
      customGetPasteResult(nameString) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          let data = []
          const names = nameString.split(';')
          data = this.remoteData.filter(item => names.includes(item.StaffName))
          resolve(data)
        })
      },
      /**
       * @method 根据组织ID获取该组织下的子级组织、子级员工列表
       * @param {String} UnitID 组织ID
       * @returns 返回带有组织、员工列表数据的promise
       */
      customGetTreeData(UnitID) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          let data = []
          if (UnitID === 0) {
            data = {
              unit: [{ UnitID: 1, UnitName: '组织一' }, { UnitID: 2, UnitName: '组织二' }]
            }
          }
          if (UnitID === 1) {
            data = {
              staff: [ { StaffName: 'fourli(李四)', StaffID: 288888 }, { StaffName: 'fivewang(王五)', StaffID: 123321 }],
              unit: [{ UnitID: 3, UnitName: '组织三' }]
            }
          }
          if (UnitID === 2) {
            data = {
              staff: [{ StaffName: 'sevenchen(陈七)', StaffID: 233333 }, { StaffName: 'ninelin(林九)', StaffID: 111111 }]
            }
          }
          if (UnitID === 3) {
            data = {
              staff: [{ StaffName: 'sixzhao(赵六)', StaffID: 666666 }]
            }
          }
          resolve(data)
        })
      },
      /**
       * @method 根据组织ID获取该组织下子级员工列表
       * @param {String} UnitID 组织ID
       * @returns 返回带有员工列表数据的promise
       */
      customGetChildrenData(UnitID) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          let data = []
          if (UnitID === 1) {
            data = [
              { StaffName: 'fourli(李四)', StaffID: 288888 },
              { StaffName: 'fivewang(王五)', StaffID: 123321 },
              // { StaffName: 'sixzhao(赵六)', StaffID: 666666 }
            ]
          }
          if (UnitID === 2) {
            data = [
              { StaffName: 'sevenchen(陈七)', StaffID: 233333 },
              { StaffName: 'ninelin(林九)', StaffID: 111111 }
            ]
          }
          if (UnitID === 3) {
            data = [
              { StaffName: 'sixzhao(赵六)', StaffID: 666666 }
            ]
          }
          resolve(data)
        })
      }
    }
  }
</script>
```
:::

### StaffSelector Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | string/number/Array | — | — |
| multiple | 是否多选 | boolean | — | false |
| size | 输入框尺寸 | string | medium/small | — |
| search | 是否模糊搜索 | boolean | — | true |
| disabled | 是否禁用 | boolean | — | false |
| showTotal | 多选且非textarea模式下，是否显示后置的已选数量 | boolean | — | true |
| showFullTag | 是否在输入框中展示完整的tag | boolean | — | false |
| placeholder | 占位符 | string | — | — |
| selectClass | 选择框自定义类名 | string | — | — |
| modalClass | 弹窗自定义类名 | string | — | — |
| modalWidth | 弹窗自定义宽度 | string | 参考Modal弹窗组件width | '750px' |
| modalAppendToBody | 弹窗自身是否插入至 body 元素上。 | boolean | — | false |
| includeDimission | 是否包含离职员工 | boolean | — | false |
| includeOnBoarding | 是否包含待入职员工 | boolean | — | false |
| includePartTimePost | 是否显示组织下的兼岗员工 | boolean | — | false |
| useFormerNameSearch | 是否使用曾用名搜索 | boolean | — | false |
| defaultExpandedKeys | 一级默认展开的节点的unitID的数组 | array | — | [] |
| range | 限制选项范围，具体见下表 | Object | — | — |
| props | 数据字段别名，具体见下表 | object | — | — |
| getDataList | 通过关键字获取对应员工的方法 | Function | — | — |
| getPasteResult | 通过姓名字段串获取对应员工的方法 | Function | — | — |
| getTreeData | 通过组织标识获取其子组织、子员工的方法 | Function | — | — |
| getChildrenData | 通过组织标识获取其下所有员工的方法 | Function | — | — |

### StaffSelector Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项 |

### StaffSelector Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| setSelected | 用于外部直接设置选中项 | 包含StaffName、StaffID、Avator属性的对象或其组成的数组 |
| clearSelected | 用于清空选中项 | — |

### range 
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| unitID | 组织ID, 仅选择该组织下的子级员工, 会先查对应的组织 | number/Array | — | - |
| contractCompanyID | 合同公司ID, 仅选择该合同下的员工 | number | — | - |
| contractCompanyIdList | 合同公司ID集合, 仅选择该合同下的员工 | Array | — | - |
| manageUnitIdList | 管理主体ID集合, 仅选择该管理主体下的员工 | Array | — | - |
| isContainSubStaff | 是否包含子级员工 | boolean | — | false |
| managerPositionLevelIdList | 职级ID集合，仅选择对应职级的员工。对应职级ID<a href="https://iwiki.woa.com/p/4009348756#%E7%AE%A1%E7%90%86%E8%81%8C%E7%BA%A7" target="_blank">参考文档</a> | Array | — | - |
| staffTypeIdList | 员工类型ID集合, 仅选择该员工类型的员工。对应员工类型ID<a href="https://hr-core.woa.com/web/dictionaryView/dictionary?key=30" target="_blank">参考字典表</a>下的员工类型 | Array | — | - |
### props
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| staffID | 员工ID字段名 | string | — | 'StaffID' |
| staffName | 员工姓名字段名 | string | — | 'StaffName' |
| engName | 员工英文名字段名 | string | — | 'EngName' |
| avatar | 员工头像字段名 | string | — | 'Avatar' |
| unitID | 组织ID字段名 | string | — | 'UnitID' |
| unitName | 组织名称字段名 | string | — | 'UnitName' |
| unitFullName | 组织全路径字段名 | string | — | 'UnitFullName' |