## UnitSelector 组织选择器
> 贡献者：miraclehe(何名宇)；kenoxia(夏超男)；ivanxu(徐凯)；最近更新时间：2021-11-16；

用于选择组织，涉及的数据源均为远程数据

### 基础用法

适用广泛的基础选择，提供2种方式选择组织，用 Tag 展示已选组织<br/>
<br/>1. 输入关键字搜索，使用下拉菜单展示筛选后的组织； 2. 点击右侧按钮打开弹窗，懒加载组织树<br/>
<br/>提醒: 请避免在选择后动态改变选择器的宽度，这会造成 Tag 区域样式显示问题

:::demo `v-model` 的值为当前被选中的组织选项的 **UnitID** 属性值，单选模式下需删除原有已选项才能进行再次选择。设置 `multiple` 属性即可启用多选，此时`v-model`的值为当前选中值所组成的数组
```html
<template>
  <div class="block">
    <span class="demonstration">单选模式</span>
    <sdc-unit-selector v-model="value1" selectClass="selectClass" modalClass="modalClass" showFullTag/>
  </div>
  <div class="block">
    <span class="demonstration">多选模式</span>
    <sdc-unit-selector multiple v-model="value2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: []
      }
    }
  }
</script>
```
:::

### 设置初始选中项

由于选择器所需的组织选项至少需要包含 `UnitID` 、`UnitName`, `UnitFullName` 等属性，而本地没有完整的组织数据，因此不能简单通过修改 `v-model` 值来设置初始选择项

:::demo 可通过 `setSelected` 方法设置初始选中项，它的输入参数是一个对象或该类对象组成的数组，对象应包含 `{ UnitID, UnitName, UnitFullName }` 等属性，如果用户初始选中项字段与选择器默认的不一致，可通过 `props` 属性配置，同时，用户通过change事件得到的选中项里的字段名也会修改为 `props` 里定义的名称
```html
<template>
  <div class="block">
    <sdc-unit-selector ref="selector1" v-model="value1" multiple/>
  </div>
  <div class="block">
    <sdc-unit-selector ref="selector2" v-model="value2" :props="myProps" multiple/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: [],
        myProps: {
          unitName: 'name',
          unitID: 'id',
          unitFullName: 'fullName'
        }
      }
    },
    mounted() {
      const initial1 = [{ UnitName: '企业综合部', UnitID: 24704, UnitFullName: 'TEG技术工程事业群/企业综合部' }]
      this.$refs.selector1.setSelected(initial1)
      const initial2 = [{ name: '企业综合部', id: 24704, fullName: 'TEG技术工程事业群/企业综合部' }]
      this.$refs.selector2.setSelected(initial2)
    }
  }
</script>
```
:::

### 设置根组织

通过设置 `unitID` 属性可指定根组织

:::demo 可通过 `defaultExpandedKeys` 属性设置一级默认展开节点
```html
<template>
  <div>
    <sdc-unit-selector multiple :unitID="unitID" :defaultExpandedKeys="[4791]" v-model="value"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        unitID: 4791,
        value: []
      }
    }
  }
</script>
```
:::



### 限制组织选择范围

可通过设置 `includeUnitSortIDs` 数组属性来限制组织选择范围<br/>
可通过设置 `isLimitUnitExpand` 属性来设置是否限制展开范围中最小级别的组织，默认`true`<br/>
<br/>注意：受目前接口限制，模糊搜索时需要输入比较完整的名称，故暂不建议使用

:::demo
```html
<template>
  <div class="block">
    <span class="demonstration">限制展开范围</span>
    <sdc-unit-selector multiple :includeUnitSortIDs="includeUnitSortIDs" v-model="value"/>
  </div>
  <div class="block">
    <span class="demonstration">不限制展开范围</span>
    <sdc-unit-selector multiple :includeUnitSortIDs="includeUnitSortIDs" :isLimitUnitExpand="false" v-model="value2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value: [],
        includeUnitSortIDs: [6, 1],
        value2: []
      }
    }
  }
</script>
```
:::


### 文本域

用于多行展示被选择的组织，通过设置 `textarea` 属性启用

:::demo 可通过 `height` 属性设置高度
```html
<template>
  <sdc-unit-selector v-model="value" :height="170" textarea multiple placeholder="请选择"/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
```
:::

### 尺寸

:::demo 可通过 `size` 属性指定输入框的尺寸，除了默认的大小外，提供了**medium**、**small**尺寸
```html
<template>
  <div class="block size-block">
    <span class="demonstration">默认尺寸</span>
    <sdc-unit-selector v-model="value1"/>
  </div>
  <div class="block size-block">
    <span class="demonstration">中等尺寸</span>
    <sdc-unit-selector size="medium" v-model="value2"/>
  </div>
  <div class="block size-block">
    <span class="demonstration">较小尺寸</span>
    <sdc-unit-selector size="small" v-model="value3"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: '',
        value3: ''
      }
    }
  }
</script>
```
:::

### 弹窗插入到body中

:::demo 可通过 `modalAppendToBody` 属性设置弹窗插入位置。默认false，设置为true时，弹窗会插入到body中。
```html
<template>
  <sdc-unit-selector v-model="value" modalAppendToBody/>
</template>
<script>
  export default {
    data(){
      return{
        value: ''
      }
    }
  }
</script>
```
:::

### 数据源
组织数据均从远程服务器获取，内置了2个默认数据源接口方法满足选择器的功能需求，用户如需替换自己的数据源，需按规范定义接口方法和数据结构，通过2个对应的属性传入`Function`<br/>每个`Function`接受一个输入参数用于指明需获取的组织数据，返回一个 **`Promise`** 对象，完成状态的结果值为包含**组织对象**的**数组**，**组织对象**至少包含 **`{ UnitID, UnitName, UnitFullName }`** 三个属性<sup>**[1]**</sup>

:::demo **`getDataList`** 用于输入关键字筛选组织的场景。输入参数为一个`String`类型的关键字字符串, Promise的结果值如 **[1]** 所示<br/><br/>**`getTreeData`** 用于点击组织树中的组织节点，懒加载其子节点的场景。输入参数为一个能指明组织的`UnitID`（即点击的组织节点），请注意，初始获取第一级节点时，输入的 **UnitID** 为 **0** ，Promise的结果值如 **[1]** 所示
```html
<template>
  <sdc-unit-selector v-model="value" multiple modalWidth="1000px"
    :getDataList="customGetDataList"
    :getTreeData="customGetTreeData"/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    },
    methods: {
      /**
       * @method 模糊搜索对应的组织列表
       * @param {String} name 关键字字符串
       * @returns 返回带有组织列表数据的promise
       */
      customGetDataList(name) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          const remoteData = [
            { UnitName: 'TEG技术工程事业群', UnitID: 958, UnitFullName: 'TEG技术工程事业群' },
            { UnitName: '企业综合部', UnitID: 24704, UnitFullName: 'TEG技术工程事业群/企业综合部' },
            { UnitName: 'CDG企业发展事业群', UnitID: 18051, UnitFullName: 'CDG企业发展事业群' },
            { UnitName: 'CDG职业发展委员会', UnitID: 18163, UnitFullName: 'CDG企业发展事业群/CDG职业发展委员会' },
            { UnitName: 'CDG通道决策委员会', UnitID: 18164, UnitFullName: 'CDG企业发展事业群/CDG职业发展委员会/CDG通道决策委员会'}
          ]
          let data = []
          data = remoteData.filter(item => item.UnitName.indexOf(name) !== -1)
          resolve(data)
        })
      },
      /**
       * @method 根据组织ID获取该组织下的子级组织列表
       * @param {String} UnitID 组织ID
       * @returns 返回带有组织列表数据的promise
       */
      customGetTreeData(UnitID) {
        return new Promise((resolve, reject) => {
          // 这里是模拟后端处理
          let data = []
          if (UnitID === 0) {
            data = [
              { UnitName: 'TEG技术工程事业群', UnitID: 958, UnitFullName: 'TEG技术工程事业群' },
              { UnitName: 'CDG企业发展事业群', UnitID: 18051, UnitFullName: 'CDG企业发展事业群' },
            ]
          }
          if (UnitID === 958) {
            data = [
              { UnitName: '企业综合部', UnitID: 24704, UnitFullName: 'TEG技术工程事业群/企业综合部' }
            ]
          }
          if (UnitID === 18051) {
            data = [
              { UnitName: 'CDG职业发展委员会', UnitID: 18163, UnitFullName: 'CDG企业发展事业群/CDG职业发展委员会' }
            ]
          }
          if (UnitID === 18163) {
            data = [
              { UnitName: 'CDG通道决策委员会', UnitID: 18164, UnitFullName: 'CDG企业发展事业群/CDG职业发展委员会/CDG通道决策委员会'}
            ]
          }
          resolve(data)
        })
      }
    }
  }
</script>
```
:::

### UnitSelector Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | string/number/Array | — | — |
| unitID | 根组织ID | number/Array | — | — |
| multiple | 是否多选 | boolean | — | false |
| size | 输入框尺寸 | string | medium/small | — |
| search | 是否模糊搜索 | boolean | — | true |
| disabled | 是否禁用 | boolean | — | false |
| showTotal | 多选且非textarea模式下，是否显示后置的已选数量 | boolean | — | true |
| placeholder | 占位符 | string | — | — |
| selectClass | 选择框自定义类名 | string | — | — |
| modalClass | 弹窗自定义类名 | string | — | — |
| modalWidth | 弹窗自定义宽度 | string | 参考Modal弹窗组件width | '750px' |
| modalAppendToBody | 弹窗自身是否插入至 body 元素上。 | boolean | — | false |
| showLastLevels | 是否只展示最后一级 | boolean | — | true |
| showFullTag | 是否在输入框中展示完整的tag | boolean | — | false |
| filterEnableFlag | 是否只包含有效组织 | boolean | — | true |
| includeVirtualUnit | 是否包含虚拟组织 | boolean | — | false |
| containUnitIDPath | 选中项是否包含完整组织ID路径（即UnitIDPath属性）| boolean | — | false |
| containUnitLocationCode | 选中项是否包含完整组织code编码（即UnitLocationCode属性）| boolean | — | false |
| includeUnitSortIDs | 限制组织选择范围 | number Array | 0-公司、6-bg、8-线、1-部门、7-中心、2-组 | - |
| isLimitUnitExpand | 是否限制展开范围中最小级别的组织, 仅限制组织选择范围时有效 | boolean | - | true |
| defaultExpandedKeys | 一级默认展开的节点的unitID的数组 | array | — | [] |
| props | 数据字段别名，具体见下表 | object | — | — |
| getDataList | 通过关键字获取对应组织的方法 | Function | — | — |
| getTreeData | 通过组织标识获取其子组织的方法 | Function | — | — |

### UnitSelector Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项 |

### UnitSelector Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| setSelected | 用于外部直接设置选中项 | 包含unitName、unitID、unitFullName属性的对象或其组成的数组 |
| clearSelected | 用于清空选中项 | — |

### props
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| unitID | 组织ID字段名 | string | — | 'UnitID' |
| unitName | 组织名称字段名 | string | — | 'UnitName' |
| unitFullName | 组织完整名称字段名 | string | — | 'UnitFullName' |
| unitOwnershipTypeId | 组织管理归属类型Id字段名 | string | — | 'UnitOwnershipTypeId' |
| unitOwnershipTypeNameCn | 组织管理归属类型名称-中文字段名 | string | — | 'UnitOwnershipTypeNameCn' |
| unitOwnershipTypeNameEn | 组织管理归属类型名称-英文字段名 | string | — | 'UnitOwnershipTypeNameEn' |