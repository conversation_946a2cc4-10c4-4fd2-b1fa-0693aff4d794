## 表单元素
> 贡献者：j<PERSON><PERSON>(刘志杰)；cxyxhhuang(黄鑫杰)；最近更新时间：2020-10-21；

复用Element UI表单元素，详细用法参考 <a href="https://element.eleme.cn/#/zh-CN/component/button" target="_blank">Element UI文档</a> ，下面代码示例。

### 按钮

:::demo
```html
<el-row class="demo-line">
  <el-button>Default</el-button>
  <el-button type="primary">Primary</el-button>
  <el-button type="success">Success</el-button>
  <el-button type="info">Info</el-button>
  <el-button type="warning">Warning</el-button>
  <el-button type="danger">Danger</el-button>
</el-row>
<el-row class="demo-line">
  <el-button disabled>Disabled</el-button>
  <el-button disabled type="primary">Primary</el-button>
  <el-button disabled type="success">Success</el-button>
  <el-button disabled type="info">Info</el-button>
  <el-button disabled type="warning">Warning</el-button>
  <el-button disabled type="danger">Danger</el-button>
</el-row>
<el-row class="demo-line">
  <el-button plain>Plain</el-button>
  <el-button type="primary" plain>Primary</el-button>
  <el-button type="success" plain>Success</el-button>
  <el-button type="info" plain>Info</el-button>
  <el-button type="warning" plain>Warning</el-button>
  <el-button type="danger" plain>Danger</el-button>
</el-row>
<el-row class="demo-line">
  <el-button round>Round</el-button>
  <el-button type="primary" round>Primary</el-button>
  <el-button type="success" round>Success</el-button>
  <el-button type="info" round>Info</el-button>
  <el-button type="warning" round>Warning</el-button>
  <el-button type="danger" round>Danger</el-button>
</el-row>
<el-row class="demo-line">
  <el-button icon="el-icon-search" circle></el-button>
  <el-button type="primary" icon="el-icon-edit" circle></el-button>
  <el-button type="success" icon="el-icon-check" circle></el-button>
  <el-button type="info" icon="el-icon-message" circle></el-button>
  <el-button type="warning" icon="el-icon-star-off" circle></el-button>
  <el-button type="danger" icon="el-icon-delete" circle></el-button>
</el-row>
<el-row class="demo-line">
  <el-button>Default</el-button>
  <el-button size="medium">Medium</el-button>
  <el-button size="small">Small</el-button>
  <el-button size="mini">Mini</el-button>
</el-row>
```
:::

### 输入框

:::demo
```html
<template>
<el-row class="demo-line" style="width: 180px">
  <el-input placeholder="请输入" v-model="input"></el-input>
</el-row>
<el-row class="demo-line">
  <el-input-number v-model="inputNumber" :min="1" :max="10"></el-input-number>
</el-row>
<el-row class="demo-line" style="width: 500px">
  <el-input
    type="textarea"
    :rows="5"
    placeholder="请输入内容"
    v-model="textarea"
    maxlength="50"
    show-word-limit
    >
  </el-input>
</el-row>
</template>
<script>
export default {
  data(){
    return{
      input: 'sdc',
      inputNumber: 3,
      textarea:'sdc',
    }
  }
}
</script>
```
:::


### 选择器

:::demo
```html
<template>
<el-row>
  <el-select v-model="selectValue" placeholder="Select">
    <el-option
      v-for="item in selectOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</el-row>
</template>
<script>
export default {
  data(){
    return{
      selectOptions: [
        {
          value: 'Option1',
          label: 'Option1'
        },
        {
          value: 'Option2',
          label: 'Option2'
        },
        {
          value: 'Option3',
          label: 'Option3'
        },
        {
          value: 'Option4',
          label: 'Option4'
        },
        {
          value: 'Option5',
          label: 'Option5'
        }
      ],
      selectValue: '',
    }
  }
}
</script>
```
:::


### 级联选择器

:::demo
```html
<template>
<el-row>
  <el-cascader :options="cascadeOptions" v-model="cascaderValue"></el-cascader>
</el-row>

</template>
<script>
export default {
  data(){
    return{
      cascadeOptions: [
        {
          value: 'guide',
          label: 'Guide',
          children: [
            {
              value: 'disciplines',
              label: 'Disciplines',
              children: [
                {
                  value: 'consistency',
                  label: 'Consistency'
                },
                {
                  value: 'feedback',
                  label: 'Feedback'
                }
              ]
            }
          ]
        },
        {
          value: 'resource',
          label: 'Resource',
          children: [
            {
              value: 'axure',
              label: 'Axure Components'
            },
            {
              value: 'sketch',
              label: 'Sketch Templates'
            },
            {
              value: 'docs',
              label: 'Design Documentation'
            }
          ]
        }
      ],
      cascaderValue: [],
    }
  }
}
</script>
```
:::

### 单选框

:::demo
```html
<template>
<el-row class="demo-line">
  <el-radio v-model="radio" label="1">Option A</el-radio>
  <el-radio v-model="radio" label="2">Option B</el-radio>
</el-row>
<el-row class="demo-line">
  <el-radio-group v-model="radio1">
    <el-radio-button label="New York"></el-radio-button>
    <el-radio-button label="Washington"></el-radio-button>
    <el-radio-button label="Los Angeles"></el-radio-button>
    <el-radio-button label="Chicago"></el-radio-button>
  </el-radio-group>
</el-row>
<el-row class="demo-line">
  <el-radio v-model="radio2" label="1" >Option A</el-radio>
  <el-radio v-model="radio2" label="2" >Option B</el-radio>
</el-row>
</template>
<script>
  export default {
    data(){
      return{
        radio: '1',
        radio1: 'Washington',
        radio2: '1',
      }
    }
  }
</script>
```
:::

### 复选框

:::demo
```html
<template>
<el-row class="demo-line">
  <el-checkbox v-model="checked">Option</el-checkbox>
</el-row>
<el-row class="demo-line">
  <el-checkbox-group v-model="checked1">
    <el-checkbox-button v-for="city in ['Shanghai', 'Beijing', 'Guangzhou', 'Shenzhen']" :label="city" :key="city">{{city}}</el-checkbox-button>
  </el-checkbox-group>
</el-row>
<el-row class="demo-line">
  <el-checkbox v-model="checked2" label="Option1" ></el-checkbox>
  <el-checkbox v-model="checked2" label="Option1" disabled ></el-checkbox>
</el-row>
</template>
<script>
export default {
  data(){
    return{
      checked: true,
      checked1: ['Shanghai'],
      checked2: true,
    }
  }
}
</script>
```
:::


### 滑块

:::demo
```html
<template>
  <div style="font-size: 14px">
    <span>未选中：</span>
    <el-switch v-model="value1"></el-switch>
    <span style="margin-left: 20px">选中：</span>
    <el-switch v-model="value2"></el-switch>
    <span style="margin-left: 20px">未选中禁用：</span>
    <el-switch v-model="value3" :disabled="true"></el-switch>
    <span style="margin-left: 20px">选中禁用：</span>
    <el-switch v-model="value4" :disabled="true"></el-switch>
  </div>
</template>
<script>
export default {
  data(){
    return {
      value1: false,
      value2: true,
      value3: false,
      value4: true
    }
  }
}
</script>
```
:::


