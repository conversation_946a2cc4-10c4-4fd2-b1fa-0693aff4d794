html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue',Helvetica,'PingFang SC','Hiragino Sans GB','Microsoft YaHei',SimSun,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;

  &.is-component {
    overflow: hidden;
  }
}

#app {
  height: 100%;

  &.is-component {
    overflow-y: hidden;

    .main-cnt {
      padding: 0;
      margin-top: 0;
      height: 100%;
      min-height: auto;
    }

    .headerWrapper {
      position: fixed;
      width: 100%;
      left: 0;
      top: 0;
      z-index: 1500;

      .container {
        padding: 0;
      }
    }
  }
}

a {
  // color: #409EFF;
  text-decoration: none;
}

code {
  background-color: #f9fafc;
  padding: 0 4px;
  border: 1px solid #eaeefb;
  border-radius: 4px;
}

button, input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

.hljs {
  line-height: 1.8;
  font-family: Menlo, Monaco, Consolas, Courier, monospace;
  font-size: 12px;
  padding: 18px 24px;
  background-color: #fafafa;
  border: solid 1px #eaeefb;
  margin-bottom: 25px;
  border-radius: 4px;
  -webkit-font-smoothing: antialiased;
}

// .main-cnt {
//   margin-top: -80px;
//   padding: 80px 0 340px;
//   box-sizing: border-box;
//   min-height: 100%;
// }

.container,
.page-container {
  width: 1140px;
  padding: 0;
  margin: 0 auto;
}

.page-container {
  h2 {
    font-size: 28px;
    color: #1f2d3d;
    margin: 0;
  }
  h3 {
    font-size: 22px;
  }
  h2, h3, h4, h5 {
    font-weight: normal;
    color: #1f2f3d;

    &:hover a {
      opacity: .4;
    }

    a {
      float: left;
      margin-left: -20px;
      opacity: 0;
      cursor: pointer;

      &:hover {
        opacity: .4;
      }
    }
  }
  h3,h4,h5{
    margin: 1.33em 0;
  }
  p {
    font-size: 14px;
    line-height: 1.5em;
    margin: 1em 0;
  }

  .tip {
    padding: 8px 16px;
    background-color: #ECF8FF;
    border-radius: 4px;
    border-left: #50bfff 5px solid;
    margin: 20px 0;

    code {
      background-color: rgba(255, 255, 255, .7);
      color: #445368;
    }
  }

  .warning {
    padding: 8px 16px;
    background-color: #fff6f7;
    border-radius: 4px;
    border-left: #FE6C6F 5px solid;
    margin: 20px 0;

    code {
      background-color: rgba(255, 255, 255, .7);
      color: #445368;
    }
  }
  blockquote{
    border-left: 5px solid #abd5fd;
    padding-left: 10px;
    margin-bottom: 30px;
    &,p{
      color: #ACACAC;
    }
  }
  a{
    cursor: pointer;
    color: #409EFF;
  }
  ol{
    margin-left: 20px;
    li{
      font-size: 14px;
      color: #5e6d82;
      line-height: 1.5em;
      margin: 1em 0;
    }
  }
}
.demo {
  margin: 20px 0;
}
details summary{
  cursor: pointer;
  color: #409EFF;
}
summary:focus{
  outline: none;
  box-shadow: none;
}

@media (max-width: 1140px) {
  .container,
  .page-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .container,
  .page-container {
    padding: 0 20px;
  }

  #app.is-component .headerWrapper .container {
    padding: 0 12px;
  }
}
