.demo-post-selector {
  .block {
    padding: 30px 0;
    border-right: solid 1px #eff2f6;
    width: 50%;
    box-sizing: border-box;
    &.size-block {
      width: 33%;
      .sdc-post-selector {
        width: 100%;
      }
      &:nth-child(2) {
        padding-left: 20px;
        padding-right: 20px;
      } 
    }
    &:first-child {
      padding-right: 20px;
    }
    &:last-child {
      border-right: none;
      padding-left: 20px;
    }
  }
  .demonstration {
    display: block;
    color: #8492a6;
    text-align: center;
    font-size: 14px;
    margin-bottom: 20px;
  }
  .sdc-post-selector {
    width: 390px;
  }
}

.demo-post-selector .source > div {
  display: flex;
}